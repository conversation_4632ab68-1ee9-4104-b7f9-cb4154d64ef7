import 'dart:developer';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:easy_social_share/easy_social_share.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({
    super.key,
  });

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  EasySocialShare easySocialShare = EasySocialShare();

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
        title: "Share Feature",
        home: Scaffold(
          body: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                child: const Text("ShareToSystem"),
                onPressed: () async {
                  FilePickerResult? result =
                      await FilePicker.platform.pickFiles(
                    type: FileType.image,
                    allowMultiple: false,
                  );

                  if (result != null && result.paths.isNotEmpty) {
                    _shareToSystem("message", result.paths[0]!);
                  }
                },
              ),
            ],
          ),
        ));
  }

  // Future<void> _shareToWhatsApp(String message, String filePath) async {
  //   await easySocialShare.android.shareToSMS(message, filePath);
  // }

  Future<void> _shareToSystem(String message, String filePath) async {
    final String result = await easySocialShare.android.shareToSystem(
      'Share Image',
      message,
      filePath,
    );

    log('share result: $result');
  }
}
