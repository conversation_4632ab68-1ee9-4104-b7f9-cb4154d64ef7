# Easy Social Share Example

This example app demonstrates how to use the [easy_social_share](../README.md) Flutter plugin to share content to various social media platforms and messaging apps.

## Features Demonstrated

The example app showcases the following sharing capabilities:

### Android Platform
- **WhatsApp**: Share text messages and files (single or multiple)
- **Telegram**: Share text messages and files (single or multiple)
- **Twitter**: Share text messages with optional file attachments
- **Instagram**:
  - Direct messages
  - Feed posts (single or multiple images)
  - Reels (videos)
  - Stories (with customizable backgrounds and stickers)
- **Facebook**:
  - Feed posts with hashtags and files
  - Stories (with customizable backgrounds and stickers)
- **TikTok**: Share content to TikTok status (Android only)
- **Messenger**: Share text messages
- **SMS**: Share text messages and files (single or multiple)
- **System Share**: Use the native Android share dialog
- **Clipboard**: Copy text to clipboard

### Cross-Platform
- **Get Installed Apps**: Check which social media apps are installed on the device

## Getting Started

### Prerequisites
- Flutter SDK (>=3.32.4)
- Dart SDK (>=3.0.0 <4.0.0)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/tentram/flutter_packages.git
cd flutter_packages/packages/easy_social_share/example
```

2. Install dependencies:
```bash
flutter pub get
```

3. Run the example app:
```bash
flutter run
```

## Usage Example

The main example demonstrates sharing an image to the system share dialog:

```dart
import 'package:easy_social_share/easy_social_share.dart';
import 'package:file_picker/file_picker.dart';

class MyApp extends StatefulWidget {
  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  EasySocialShare easySocialShare = EasySocialShare();

  Future<void> _shareToSystem(String message, String filePath) async {
    final String result = await easySocialShare.android.shareToSystem(
      'Share Image',
      message,
      filePath,
    );
    print('Share result: $result');
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        body: Center(
          child: ElevatedButton(
            child: Text("Share to System"),
            onPressed: () async {
              FilePickerResult? result = await FilePicker.platform.pickFiles(
                type: FileType.image,
                allowMultiple: false,
              );

              if (result != null && result.paths.isNotEmpty) {
                _shareToSystem("Check out this image!", result.paths[0]!);
              }
            },
          ),
        ),
      ),
    );
  }
}
```

## Platform Support

| Platform | Supported |
|----------|-----------|
| Android  | ✅        |
| iOS      | ✅        |

## Dependencies

- `flutter`: Flutter SDK
- `easy_social_share`: The main plugin (path dependency to parent directory)
- `file_picker`: For selecting files to share
- `cupertino_icons`: iOS-style icons

## Complete API Reference

### Main Class

#### `EasySocialShare`
The main entry point for the plugin.

```dart
EasySocialShare easySocialShare = EasySocialShare();
```

**Properties:**
- `android` - Access to Android-specific sharing methods
- `iOS` - Access to iOS-specific sharing methods

**Methods:**
- `Future<Map<String, bool>> getInstalledApps()` - Get a map of installed social media apps

### Android Platform API (`easySocialShare.android`)

#### WhatsApp
- `Future<String> shareToWhatsapp(String message, String? filePath)` - Share text message with optional file
- `Future<String> shareFilesToWhatsapp(List<String> filePaths)` - Share multiple files to WhatsApp

#### Telegram
- `Future<String> shareToTelegram(String message, String? filePath)` - Share text message with optional file
- `Future<String> shareFilesToTelegram(List<String> filePaths)` - Share multiple files to Telegram

#### Twitter
- `Future<String> shareToTwitter(String message, String? filePath)` - Share text message with optional file attachment

#### Instagram
- `Future<String> shareToInstagramDirect(String message)` - Share text message to Instagram Direct
- `Future<String> shareToInstagramFeed(String message, String? filePath)` - Share to Instagram feed with optional image
- `Future<String> shareFilesToInstagramFeed(List<String> imagePaths)` - Share multiple images to Instagram feed
- `Future<String> shareToInstagramReels(List<String> videoPaths)` - Share videos to Instagram Reels
- `Future<String> shareToInstagramStory(String appId, {String? stickerImage, String? backgroundImage, String? backgroundVideo, String? backgroundTopColor, String? backgroundBottomColor, String? attributionURL})` - Share to Instagram Stories with customization options

#### Facebook
- `Future<String> shareToFacebook(String hashtag, List<String> filePaths)` - Share to Facebook with hashtag and files
- `Future<String> shareToFacebookStory(String appId, {String? stickerImage, String? backgroundImage, String? backgroundVideo, String? backgroundTopColor, String? backgroundBottomColor, String? attributionURL})` - Share to Facebook Stories with customization options

#### TikTok
- `Future<String> shareToTiktokStatus(List<String> filePaths)` - Share files to TikTok status (Android only)

#### Messenger
- `Future<String> shareToMessenger(String message)` - Share text message to Facebook Messenger

#### SMS
- `Future<String> shareToSMS(String message, String? filePath)` - Share text message with optional file via SMS
- `Future<String> shareFilesToSMS(List<String> filePaths)` - Share multiple files via SMS

#### System Share
- `Future<String> shareToSystem(String title, String message, String? filePath)` - Use native Android share dialog
- `Future<String> shareFilesToSystem(String title, List<String> filePaths)` - Share multiple files using system share dialog

#### Utility
- `Future<String> copyToClipBoard(String message)` - Copy text to clipboard

### iOS Platform API (`easySocialShare.iOS`)

#### WhatsApp
- `Future<String> shareToWhatsapp(String message)` - Share text message to WhatsApp
- `Future<String> shareImageToWhatsApp(String filePath)` - Share image to WhatsApp

#### Telegram
- `Future<String> shareToTelegram(String message)` - Share text message to Telegram

#### Twitter
- `Future<String> shareToTwitter(String message, String? filePath)` - Share text message with optional file

#### Instagram
- `Future<String> shareToInstagramDirect(String message)` - Share text message to Instagram Direct
- `Future<String> shareToInstagramFeed(String imagePath)` - Share image to Instagram feed
- `Future<String> shareToInstagramReels(String videoPath)` - Share video to Instagram Reels
- `Future<String> shareToInstagramStory(String appId, {String? stickerImage, String? backgroundImage, String? backgroundVideo, String? backgroundTopColor, String? backgroundBottomColor, String? attributionURL})` - Share to Instagram Stories

#### Facebook
- `Future<String> shareToFacebook(String hashtag, List<String> filePaths)` - Share to Facebook with hashtag and files
- `Future<String> shareToFacebookStory(String appId, {String? stickerImage, String? backgroundImage, String? backgroundVideo, String? backgroundTopColor, String? backgroundBottomColor, String? attributionURL})` - Share to Facebook Stories

#### TikTok
- `Future<String> shareToTiktokPost(String videoFile, String redirectUrl, TiktokFileType tiktokFileType)` - Share video to TikTok (requires additional native implementation)

#### Messenger
- `Future<String> shareToMessenger(String message)` - Share text message to Facebook Messenger

#### System Share
- `Future<String> shareToSystem(String message, {List<String>? filePaths})` - Use native iOS share dialog

#### SMS
- `Future<String> shareToSMS(String message)` - Share text message via SMS

#### Utility
- `Future<String> copyToClipBoard(String message)` - Copy text to clipboard

### Enums

#### `TiktokFileType`
Used for TikTok sharing on iOS:
- `TiktokFileType.image` - For image files
- `TiktokFileType.video` - For video files

### Advanced Usage Examples

#### Check Installed Apps
```dart
Future<void> checkInstalledApps() async {
  Map<String, bool> installedApps = await easySocialShare.getInstalledApps();
  print('WhatsApp installed: ${installedApps['whatsapp']}');
  print('Instagram installed: ${installedApps['instagram']}');
}
```

#### Share Multiple Files to WhatsApp (Android)
```dart
Future<void> shareMultipleFilesToWhatsApp() async {
  List<String> filePaths = ['/path/to/image1.jpg', '/path/to/image2.jpg'];
  String result = await easySocialShare.android.shareFilesToWhatsapp(filePaths);
  print('Share result: $result');
}
```

#### Share to Instagram Story with Custom Background
```dart
Future<void> shareToInstagramStoryWithBackground() async {
  String result = await easySocialShare.android.shareToInstagramStory(
    'your_app_id',
    backgroundImage: '/path/to/background.jpg',
    stickerImage: '/path/to/sticker.png',
    backgroundTopColor: '#FF0000',
    backgroundBottomColor: '#0000FF',
    attributionURL: 'https://your-website.com',
  );
  print('Share result: $result');
}
```

#### Share Video to TikTok (iOS)
```dart
Future<void> shareVideoToTikTok() async {
  String result = await easySocialShare.iOS.shareToTiktokPost(
    '/path/to/video.mp4',
    'https://your-redirect-url.com',
    TiktokFileType.video,
  );
  print('Share result: $result');
}
```

#### Platform-Specific Sharing
```dart
Future<void> shareBasedOnPlatform(String message, String filePath) async {
  if (Platform.isAndroid) {
    // Android supports file sharing for most platforms
    String result = await easySocialShare.android.shareToWhatsapp(message, filePath);
    print('Android share result: $result');
  } else if (Platform.isIOS) {
    // iOS has different API for image sharing
    String result = await easySocialShare.iOS.shareImageToWhatsApp(filePath);
    print('iOS share result: $result');
  }
}
```

### Return Values
All sharing methods return a `Future<String>` that contains:
- Success message when sharing is completed
- Error message if sharing fails
- Platform-specific status information

### Platform Differences

| Feature | Android | iOS | Notes |
|---------|---------|-----|-------|
| File Sharing | ✅ Most platforms | ⚠️ Limited | Android supports files for most platforms |
| Multiple Files | ✅ | ❌ | Android supports sharing multiple files |
| TikTok Status | ✅ | ❌ | Android only feature |
| TikTok Post | ❌ | ✅ | iOS only (requires additional setup) |
| System Share | ✅ | ✅ | Both platforms supported |
| Story Customization | ✅ | ✅ | Both platforms support Instagram/Facebook stories |

### Important Notes

1. **App Installation**: Always check if the target app is installed using `getInstalledApps()` before attempting to share
2. **File Paths**: Use absolute file paths when sharing files
3. **Permissions**: Ensure your app has necessary permissions for file access
4. **TikTok iOS**: Requires additional native implementation as mentioned in the plugin documentation
5. **Story Sharing**: Instagram and Facebook story sharing requires app registration and proper app IDs

### Error Handling

```dart
Future<void> shareWithErrorHandling() async {
  try {
    String result = await easySocialShare.android.shareToWhatsapp(
      'Hello World!',
      '/path/to/image.jpg'
    );

    if (result.contains('success')) {
      print('Share completed successfully');
    } else {
      print('Share failed: $result');
    }
  } catch (e) {
    print('Error during sharing: $e');
  }
}
```

## Additional Examples

For more detailed examples of sharing to specific platforms, check out the [main plugin documentation](../README.md).

## Contributing

This example is part of the easy_social_share plugin package. For contributions and issues, please visit the [main repository](https://github.com/tentram/flutter_packages).

## License

This example follows the same license as the main easy_social_share plugin.
